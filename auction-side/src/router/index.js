import {createRouter, createWebHistory} from 'vue-router'
import {PATH_NAME} from '../defined/const'
import {useCognitoAuthStore} from '../stores/cognitoAuth'

import ProductDetail from '@/components/product/detail/ProductDetail.vue'
import _TopPage from '@/components/top-page/_TopPage.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  // eslint-disable-next-line no-unused-vars
  scrollBehavior(to, from, savedPosition) {
    // Scroll to the top position when entering a new route
    return {top: 0}
  },
  routes: [
    {
      path: PATH_NAME.TOP,
      name: 'home',
      component: _TopPage,
      meta: {
        name: 'route.top',
        bodyId: 'home',
      },
    },
    {
      path: PATH_NAME.LOGIN,
      name: 'login',
      component: () => import('@/components/login/LoginPage.vue'),
      meta: {
        headerShow: true,
        footerShow: true,
        name: 'route.login',
        bodyId: 'login',
      },
    },
    // パスワード忘れ
    {
      path: PATH_NAME.REMINDER,
      name: 'reminder',
      component: () => import('@/components/reminder/ReminderPage.vue'),
      meta: {
        name: 'route.reminder',
        bodyId: 'reminder',
      },
    },
    {
      path: PATH_NAME.REGISTER,
      name: 'register',
      component: () => import('@/components/register/RegisterPage.vue'),
      meta: {
        // isRegist: true,
        name: 'route.register',
        bodyId: 'entry',
      },
    },
    {
      path: PATH_NAME.COGNITO_REGISTER,
      name: 'cognito-register',
      component: () => import('@/components/register/CognitoRegister.vue'),
      meta: {
        name: 'route.cognitoRegister',
        bodyId: 'cognito-register',
      },
    },
    {
      path: PATH_NAME.ENTRY_INFO_CONFIRM,
      name: 'entry-info-complete',
      component: () => import('@/components/register/RegisterPage.vue'),
      meta: {
        isConfirm: true,
        name: 'route.register',
        bodyId: 'entry',
      },
    },
    {
      path: PATH_NAME.AUCTION_LIST,
      name: 'auction-list',
      component: () => import('@/components/product/list/ProductList.vue'),
      meta: {
        name: 'route.auctionList',
        bodyId: 'auctionList',
      },
    },
    {
      path: `${PATH_NAME.DETAIL}/:manageNo`,
      name: 'details',
      component: ProductDetail,
      meta: {
        // requireAuth: true, // Commented out to allow public access
        name: 'route.details',
        bodyClass: 'stock',
        bodyId: 'detail',
      },
    },
    {
      path: `${PATH_NAME.NOTICE_LIST}/:noticeNo`,
      name: 'notice-details',
      component: () => import('@/components/notice/detail/NoticeDetails.vue'),
      meta: {
        name: 'route.noticeDetails',
        bodyId: 'news',
      },
    },
    {
      path: `${PATH_NAME.NOTICE_LIST_IMPORTANT}/:noticeNo`,
      name: 'notice-important-details',
      component: () => import('@/components/notice/detail/NoticeDetails.vue'),
      meta: {
        name: 'route.importantNotice',
        bodyId: 'news',
      },
    },
    {
      path: '/mypage',
      component: () => import('@/components/mypage/MyPage.vue'),
      meta: {
        requireAuth: true,
        bodyClass: 'mypage',
      },
      children: [
        // Render favorite page as default(/mypage)
        {
          path: '',
          redirect: PATH_NAME.MYPAGE_FAVORITE, // 'favorite'
        },
        {
          path: 'favorite',
          name: 'favorites',
          component: () =>
            import('@/components/mypage/favorite/_MyPageFavorite.vue'),
          meta: {
            name: 'route.favorites',
            label: 'favorite.title',
          },
        },
        {
          path: 'bidding',
          name: 'bidding',
          component: () =>
            import('@/components/mypage/bidding/_MyPageBidding.vue'),
          meta: {
            name: 'route.bidding',
            label: 'auction.bidOngoing',
          },
        },
        {
          path: 'bid-history',
          name: 'bid-history',
          component: () =>
            import('@/components/mypage/history/_MyPageHistory.vue'),
          meta: {
            name: 'route.bidHistory',
            label: 'auction.bidHistory',
          },
        },
        {
          path: 'account',
          name: 'my-page-edit',
          component: () =>
            import('@/components/mypage/account/_MyPageAccount.vue'),
          meta: {
            isRegist: true,
            name: 'route.myPage',
            bodyId: '',
          },
        },
      ],
    },
    {
      path: PATH_NAME.MYPAGE_EDIT_CONFIRM,
      name: 'my-page-confirm',
      component: () => import('@/components/mypage/account/_MyPageAccount.vue'),
      meta: {
        requireAuth: true,
        isConfirm: true,
        name: 'route.myPageEditConfirm',
        bodyId: 'entry',
      },
    },
    {
      path: PATH_NAME.PROFILE,
      name: 'profile',
      component: () => import('@/components/guide-pages/CompanyProfile.vue'),
      meta: {
        name: 'route.companyOverview',
        bodyId: 'profile',
      },
    },
    {
      path: PATH_NAME.TERMS,
      name: 'terms',
      component: () => import('@/components/guide-pages/Terms.vue'),
      meta: {
        name: 'route.terms',
        bodyId: 'terms',
      },
    },
    {
      path: PATH_NAME.PRIVACY,
      name: 'privacy',
      component: () => import('@/components/guide-pages/PrivacyPage.vue'),
      meta: {
        name: 'route.privacy',
        bodyId: 'privacy',
      },
    },
    {
      path: PATH_NAME.INQUIRY,
      name: 'inquiry',
      component: () => import('@/components/inquiry/InquiryPage.vue'),
      meta: {
        name: 'route.inquiry',
        bodyId: 'inquiry',
      },
    },
    {
      path: PATH_NAME.INQUIRY_CONFIRM,
      name: 'inquiry-confirm',
      component: () => import('@/components/inquiry/InquiryPage.vue'),
      meta: {
        name: 'route.inquiryConfirm',
        bodyId: 'inquiry',
      },
    },
    {
      path: PATH_NAME.GUIDE,
      name: 'guide',
      component: () => import('@/components/guide-pages/GuidePage.vue'),
      meta: {
        name: 'route.firstTime',
        bodyId: 'guide',
      },
    },
    {
      path: PATH_NAME.ORDER_CONTRACT,
      name: 'order-contract',
      component: () => import('@/components/guide-pages/OrderContract.vue'),
      meta: {
        name: 'route.toshuho',
        bodyId: 'tokushoho',
      },
    },
    // {
    //   path: '/:pathMatch(.*)*',
    //   component: TopView
    // }
  ],
})

router.beforeEach(async (to, from, next) => {
  const cognitoAuth = useCognitoAuthStore()

  // Scroll to top for new routes (unless there's a hash)
  if (!to.hash) {
    window.scrollTo({top: 0})
  }

  // Try to refresh authentication session to get current state
  try {
    await cognitoAuth.fetchAuthSession()
  } catch (error) {
    console.warn('Failed to fetch auth session:', error)
  }

  // Prevent authenticated users from accessing login page
  if (cognitoAuth.isAuthenticated && to.name === 'login') {
    // Redirect to previous page or default to MyPage
    const redirectPath =
      from.path !== '/login' ? from.path : PATH_NAME.MYPAGE_FAVORITE
    return next({path: redirectPath})
  }

  // Protect routes that require authentication
  if (to?.meta.requireAuth && !cognitoAuth.isAuthenticated) {
    // Store the intended destination for redirect after login
    const redirectQuery = to.fullPath !== '/' ? {redirect: to.fullPath} : {}
    return next({name: 'login', query: redirectQuery})
  }

  return next()
})

/**
 * S3へデプロイ後に動的にインポートされたモジュールが取得できなかった場合のエラーハンドリング。
 * onErrorフックを使用してエラーが発生した場合にルートパス（`/`）に遷移
 * 参照: https://github.com/vitejs/vite/issues/11804#issuecomment-1406182566
 */
router.onError(error => {
  console.log('router error:', error)
  if (error.message.includes('Failed to fetch dynamically imported module')) {
    console.warn(`${error.message}, force reload.`)
    // 開発環境では強制リロードを行わない（Viteの環境変数を使用）
    const isDevelopment = import.meta.env.DEV
    if (!isDevelopment) {
      window.location.href = '/'
    } else {
      console.warn('Skipping force reload in development environment')
    }
    return
  }
  console.error('Unhandled router error:', error)
})

export default router
