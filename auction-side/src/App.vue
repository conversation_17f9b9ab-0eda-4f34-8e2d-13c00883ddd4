<script setup>
  import {computed, onBeforeMount, onMounted, watch} from 'vue'
  import {useAuthStore} from './stores/auth'
  import {useCommonConstantsStore} from './stores/common-constants'

  import {useRoute} from 'vue-router'
  import {useLocale} from 'vuetify'
  import LoaderModal from './components/parts/LoaderModal.vue'
  // import ShowMessageModal from './components/parts/ShowMessageDialog.vue'
  import {scrollToAnker} from './composables/common'
  import {useLanguageStore} from './stores/language'

  const {t, current: locale} = useLocale()
  const auth = useAuthStore()
  const route = useRoute()
  const commonConstants = useCommonConstantsStore()
  const languageStore = useLanguageStore()

  const isSp = window.matchMedia('(max-width: 767px)')

  const bodyId = computed(() => route.meta.bodyId || '')
  const bodyClass = computed(() => route.meta.bodyClass || '')
  const mainClass = computed(() => route.meta.mainClass || '')

  const onLoadResize = () => {
    if (isSp.matches) {
      $("meta[name='viewport']").attr(
        'content',
        'width=device-width,initial-scale=1'
      )
    } else {
      $("meta[name='viewport']").attr('content', 'width=1200')
    }
  }

  onBeforeMount(() => {
    auth.reloadCookies()
    locale.value = languageStore.language
  })

  onMounted(() => {
    window.addEventListener('resize', onLoadResize)
    const urlHash = location.hash
    if (urlHash) {
      $('body,html').stop().scrollTop(0)
      setTimeout(() => {
        scrollToAnker(urlHash)
      }, 100)
    }

    // Load constants
    // TODO
    // commonConstants.getConstants()
  })

  watch(
    () => languageStore.language,
    () => {
      window.document.title = t('siteTitle')
    }
  )
</script>

<template>
  <div :id="bodyId" :class="bodyClass">
    <!-- <Header v-if="route.meta.headerShow !== false" /> -->
    <v-app id="main" :class="mainClass">
      <v-main class="stock">
        <RouterView />
      </v-main>
    </v-app>
    <!-- <Footer v-if="route.meta.footerShow !== false" /> -->
    <LoaderModal />
    <!-- <ShowMessageModal /> -->
  </div>
</template>
