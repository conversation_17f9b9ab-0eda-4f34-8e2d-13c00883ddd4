import {mount} from '@vue/test-utils'
import {beforeEach, describe, expect, it, vi} from 'vitest'
import {createRouter, createWebHistory} from 'vue-router'
import LoginPage from '../LoginPage.vue'

// Mock the Cognito auth store
const mockLogin = vi.fn()
const mockCognitoAuthStore = {
  login: mockLogin,
  user: null,
  isAuthenticated: false,
}

vi.mock('../../../stores/cognitoAuth', () => ({
  useCognitoAuthStore: () => mockCognitoAuthStore,
}))

// Create a mock router
const router = createRouter({
  history: createWebHistory(),
  routes: [
    {path: '/', component: {template: '<div>Home</div>'}},
    {path: '/login', component: {template: '<div>Login</div>'}},
    {path: '/reminder', component: {template: '<div>Reminder</div>'}},
  ],
})

describe('LoginPage', () => {
  let wrapper

  beforeEach(() => {
    vi.clearAllMocks()
    wrapper = mount(LoginPage, {
      global: {
        plugins: [router],
      },
    })
  })

  it('renders the login form', () => {
    expect(wrapper.find('h2 .ttl').text()).toBe('ログイン')
    expect(wrapper.find('h2 .sub').text()).toBe('login')
  })

  it('displays all required form fields', () => {
    // Check for email input
    const emailInput = wrapper.find('input[type="text"]')
    expect(emailInput.exists()).toBe(true)

    // Check for password input
    const passwordInput = wrapper.find('input[type="password"]')
    expect(passwordInput.exists()).toBe(true)
    // Check for remember login checkbox
    const checkboxes = wrapper.findAll('input[type="checkbox"]')
    expect(checkboxes.length).toBeGreaterThan(0)
    const rememberCheckbox = checkboxes[0]
    expect(rememberCheckbox.exists()).toBe(true)

    // Check for terms agreement checkbox
    const termsCheckbox = wrapper.find('#rule-chk')
    expect(termsCheckbox.exists()).toBe(true)

    // Check for submit button
    const submitButton = wrapper.find('#sbm-login')
    expect(submitButton.exists()).toBe(true)
    expect(submitButton.attributes('value')).toBe('ログイン')
  })

  it('does not show errors on initial load', () => {
    expect(wrapper.vm.showErrors).toBe(false)
    expect(wrapper.vm.loginErrors).toEqual([])
    expect(wrapper.find('.id-pass-err').exists()).toBe(false)
  })

  it('validates required fields', async () => {
    // Try to submit empty form
    await wrapper.find('form').trigger('submit')
    await wrapper.vm.$nextTick()

    expect(wrapper.vm.showErrors).toBe(true)
    expect(wrapper.vm.formErrors.email).toBe('ログインIDを入力してください')
    expect(wrapper.vm.formErrors.password).toBe('パスワードを入力してください')
    expect(wrapper.vm.formErrors.agreeToTerms).toBe(
      '参加規約に同意してください'
    )
  })

  it('validates password length', async () => {
    const passwordInput = wrapper.find('input[type="password"]')

    // Test too short
    await passwordInput.setValue('short')
    await passwordInput.trigger('blur')
    expect(wrapper.vm.formErrors.password).toBe(
      'パスワードは8～14文字で入力してください'
    )

    // Test valid length
    await passwordInput.setValue('validpass')
    await passwordInput.trigger('blur')
    expect(wrapper.vm.formErrors.password).toBe('')
  })

  it('enables submit button when form is valid', async () => {
    // Fill in valid form data
    await wrapper.find('input[type="text"]').setValue('testuser1')
    await wrapper.find('input[type="password"]').setValue('testpass1')
    await wrapper.find('#rule-chk').setChecked(true)

    await wrapper.vm.$nextTick()

    expect(wrapper.vm.isFormValid).toBe(true)
    expect(wrapper.find('#sbm-login').attributes('disabled')).toBeUndefined()
  })

  it('calls login function on form submission', async () => {
    mockLogin.mockResolvedValue({type: 'SUCCESS'})

    // Fill in valid form data
    await wrapper.find('input[type="text"]').setValue('testuser1')
    await wrapper.find('input[type="password"]').setValue('testpass1')
    await wrapper.find('#rule-chk').setChecked(true)

    // Submit form
    await wrapper.find('form').trigger('submit')

    expect(mockLogin).toHaveBeenCalledWith('testuser1', 'testpass1')
  })

  it('handles successful login', async () => {
    mockLogin.mockResolvedValue({type: 'SUCCESS'})
    const routerPushSpy = vi.spyOn(router, 'replace')

    // Fill in valid form data and submit
    await wrapper.find('input[type="text"]').setValue('testuser1')
    await wrapper.find('input[type="password"]').setValue('testpass1')
    await wrapper.find('#rule-chk').setChecked(true)
    await wrapper.find('form').trigger('submit')

    await wrapper.vm.$nextTick()

    expect(routerPushSpy).toHaveBeenCalledWith('/')
  })

  it('handles login errors', async () => {
    const errorMessage = 'ログインIDまたはパスワードが正しくありません。'
    mockLogin.mockRejectedValue(new Error(errorMessage))

    // Fill in valid form data and submit
    await wrapper.find('input[type="text"]').setValue('testuser1')
    await wrapper.find('input[type="password"]').setValue('testpass1')
    await wrapper.find('#rule-chk').setChecked(true)
    await wrapper.find('form').trigger('submit')

    await wrapper.vm.$nextTick()

    expect(wrapper.vm.showErrors).toBe(true)
    expect(wrapper.vm.loginErrors).toContain(errorMessage)
    expect(wrapper.find('.id-pass-err').exists()).toBe(true)
  })

  it('handles new password required', async () => {
    const message = '初回ログイン時はパスワードの変更が必要です。'
    mockLogin.mockResolvedValue({
      type: 'NEW_PASSWORD_REQUIRED',
      message,
    })

    // Fill in valid form data and submit
    await wrapper.find('input[type="text"]').setValue('testuser1')
    await wrapper.find('input[type="password"]').setValue('testpass1')
    await wrapper.find('#rule-chk').setChecked(true)
    await wrapper.find('form').trigger('submit')

    await wrapper.vm.$nextTick()

    expect(wrapper.vm.showErrors).toBe(true)
    expect(wrapper.vm.loginErrors).toContain(message)
  })

  it('shows loading state during login', async () => {
    // Create a promise that we can control
    let resolveLogin
    const loginPromise = new Promise(resolve => {
      resolveLogin = resolve
    })
    mockLogin.mockReturnValue(loginPromise)

    // Fill in valid form data and submit
    await wrapper.find('input[type="text"]').setValue('testuser1')
    await wrapper.find('input[type="password"]').setValue('testpass1')
    await wrapper.find('#rule-chk').setChecked(true)
    await wrapper.find('form').trigger('submit')

    await wrapper.vm.$nextTick()

    // Check loading state
    expect(wrapper.vm.loading).toBe(true)
    expect(wrapper.find('#sbm-login').attributes('value')).toBe('処理中...')
    expect(wrapper.find('#sbm-login').attributes('disabled')).toBeDefined()

    // Resolve the login
    resolveLogin({type: 'SUCCESS'})
    await wrapper.vm.$nextTick()

    expect(wrapper.vm.loading).toBe(false)
  })

  it('navigates to reminder page when clicking forgot password link', async () => {
    const routerPushSpy = vi.spyOn(router, 'push')

    await wrapper.find('.forget-pass a').trigger('click')

    expect(routerPushSpy).toHaveBeenCalledWith('/reminder')
  })

  it('clears field errors on input', async () => {
    // First trigger validation errors
    await wrapper.find('form').trigger('submit')
    expect(wrapper.vm.formErrors.email).toBeTruthy()

    // Then type in the field
    await wrapper.find('input[type="text"]').trigger('input')
    expect(wrapper.vm.formErrors.email).toBe('')
  })
})
