# Auction-Side Registration Components

This directory contains two different registration systems for the auction-side application:

## 1. Traditional Registration (`RegisterPage.vue`)
- **Path**: `/register`
- **Purpose**: Traditional member registration with detailed company information
- **Features**: 
  - Multi-step form with confirmation
  - Company details, antique dealer license, etc.
  - Uses traditional database storage
  - Complex validation and form handling

## 2. Cognito Registration (`CognitoRegister.vue`)
- **Path**: `/cognito-register`
- **Purpose**: AWS Cognito-based authentication registration
- **Features**:
  - Simple single-step form
  - Basic member information (name, email, password, language)
  - Integrates with AWS Cognito User Pool
  - Uses deployed Lambda functions for backend processing
  - Follows admin-side authentication patterns

## Cognito Registration Component

### Usage
```javascript
// Navigate to Cognito registration
router.push('/cognito-register')
```

### Features
- **Form Fields**:
  - Member Name (会員名)
  - Email Address (メールアドレス)
  - Password (パスワード)
  - Password Confirmation (パスワード確認)
  - Language Preference (言語設定)

- **Validation**:
  - Real-time field validation
  - Password strength requirements (uppercase, lowercase, number, special character)
  - Email format validation
  - Password confirmation matching

- **Success Flow**:
  - Shows success message with created user details
  - Option to return to form or navigate to login

### Backend Integration
- **API Endpoint**: `${VITE_API_ENDPOINT}cognito-register-member`
- **Lambda Function**: `cognito-register-member`
- **Cognito User Pool**: `saas-demo2-auction` (separate from admin-side)

### Error Handling
- Handles Cognito-specific errors (email already exists, password validation, etc.)
- Network error handling
- User-friendly Japanese error messages

### Styling
- Bootstrap 5 classes for responsive design
- Custom CSS for form styling and validation states
- Consistent with auction-side design patterns

## Development Notes

### Testing the Component
1. Start the development server:
   ```bash
   cd auction-side
   npm run dev
   ```

2. Navigate to: `http://localhost:5173/cognito-register`

3. Test registration with valid data:
   - Member Name: テストユーザー
   - Email: <EMAIL>
   - Password: TestPass123!
   - Language: 日本語

### Environment Variables Required
- `VITE_API_ENDPOINT`: API Gateway endpoint for Lambda functions
- `VITE_AUCTION_USER_POOL_ID`: Cognito User Pool ID
- `VITE_AUCTION_CLIENT_ID`: Cognito App Client ID

### Dependencies
- Vue 3 Composition API
- Vue Router
- Cognito Auth Store (`useCognitoAuthStore`)
- API Composable (`useApi`)

## Integration with Existing System

The Cognito registration component is designed to work alongside the existing traditional registration system. Users can choose between:

1. **Traditional Registration** (`/register`): For full business registration with company details
2. **Cognito Registration** (`/cognito-register`): For simple user account creation

Both systems can coexist, allowing for different user types and registration flows as needed.
