resource "aws_iam_role_policy" "cognito_login_policy" {
  name = "${var.project_name}-${var.environment}-cognito-login-policy"

  role   = module.gateway-resource.lambda_iam_role_id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = [
          "cognito-idp:AdminInitiateAuth",
          "cognito-idp:AdminGetUser"
        ],
        Effect   = "Allow",
        Resource = "arn:aws:cognito-idp:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:userpool/${var.cognito_user_pool_id}"
      }
    ]
  })
}

data "aws_region" "current" {}
data "aws_caller_identity" "current" {}
