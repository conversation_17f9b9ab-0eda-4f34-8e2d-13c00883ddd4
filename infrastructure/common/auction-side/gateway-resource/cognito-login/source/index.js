const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const {
  AdminInitiateAuthCommand,
  AdminGetUserCommand,
  CognitoIdentityProviderClient,
} = require('@aws-sdk/client-cognito-identity-provider');

const pool = new PgPool();
const cognitoClient = new CognitoIdentityProviderClient({});

/**
 * Authenticates user with Cognito
 */
async function authenticateWithCognito(email, password) {
  const command = new AdminInitiateAuthCommand({
    UserPoolId: process.env.AUCTION_COGNITO_USER_POOL_ID,
    ClientId: process.env.AUCTION_COGNITO_CLIENT_ID,
    AuthFlow: 'ADMIN_NO_SRP_AUTH',
    AuthParameters: {
      USERNAME: email,
      PASSWORD: password,
    },
  });
  
  const authResult = await cognitoClient.send(command);
  console.log('Authentication successful:', authResult);
  return authResult;
}

/**
 * Gets user details from Cognito
 */
async function getCognitoUserDetails(email) {
  const command = new AdminGetUserCommand({
    UserPoolId: process.env.AUCTION_COGNITO_USER_POOL_ID,
    Username: email,
  });
  
  const userDetails = await cognitoClient.send(command);
  console.log('User details retrieved:', userDetails);
  return userDetails;
}

/**
 * Extracts tenant information from Cognito user groups
 */
function extractTenantFromGroups(userAttributes) {
  // Find the cognito:groups attribute
  const groupsAttr = userAttributes.find(attr => attr.Name === 'cognito:groups');
  if (!groupsAttr) {
    throw new Error('User not assigned to any tenant group');
  }
  
  // Extract tenant ID from group name (format: tenant-id:X)
  const groups = groupsAttr.Value || '';
  const tenantMatch = groups.match(/tenant-id:(\d+)/);
  if (!tenantMatch) {
    throw new Error('Invalid tenant group format');
  }
  
  return parseInt(tenantMatch[1]);
}

/**
 * Gets member information from database
 */
async function getMemberFromDatabase(tenantNo, email) {
  const result = await pool.query(
    'SELECT * FROM "f_get_member_by_email"($1,$2);',
    [tenantNo, email]
  );
  
  if (!result || result.length === 0) {
    throw new Error('Member not found in database');
  }
  
  return result[0];
}

/**
 * Logs the login attempt
 */
async function logLogin(tenantNo, member, email, sourceIp, userAgent) {
  await pool.query(
    'SELECT * FROM "f_insert_login"($1,$2,$3,$4,$5,$6,$7);',
    [
      tenantNo,
      member.member_no,
      member.user_no,
      email,
      email,
      sourceIp,
      userAgent,
    ]
  );
  console.log('Login logged successfully');
}

/**
 * Main login logic
 */
async function loginMember(e) {
  const params = e.body;
  const base = new Base(pool, params.languageCode);
  
  await base.startRequest(e);
  
  const { email, password } = params;
  
  if (!email || !password) {
    throw {
      status: 400,
      name: 'Missing Parameters',
      message: 'メールアドレスとパスワードを入力してください。',
    };
  }
  
  try {
    // 1. Authenticate with Cognito
    const authResult = await authenticateWithCognito(email, password);
    
    // 2. Get user details from Cognito
    const userDetails = await getCognitoUserDetails(email);
    
    // 3. Extract tenant information from groups
    const tenantNo = extractTenantFromGroups(userDetails.UserAttributes);
    
    // 4. Get member info from database
    const member = await getMemberFromDatabase(tenantNo, email);
    
    // 5. Log the login
    await logLogin(
      tenantNo,
      member,
      email,
      e.requestContext.identity.sourceIp,
      e.requestContext.identity.userAgent
    );
    
    // 6. Extract custom attributes from Cognito
    const getCustomAttribute = (name) => {
      const attr = userDetails.UserAttributes.find(attr => attr.Name === name);
      return attr ? attr.Value : null;
    };
    
    return {
      idToken: authResult.AuthenticationResult.IdToken,
      accessToken: authResult.AuthenticationResult.AccessToken,
      refreshToken: authResult.AuthenticationResult.RefreshToken,
      memberInfo: {
        memberNo: member.member_no,
        userNo: member.user_no,
        memberName: getCustomAttribute('custom:member_name') || member.free_field?.memberName || '',
        language: getCustomAttribute('custom:language_code') || member.free_field?.language || 'ja',
        email: email,
        tenantNo: tenantNo,
      },
    };
    
  } catch (error) {
    console.error('Login error:', error);
    
    // Handle specific Cognito errors
    if (error.name === 'NotAuthorizedException') {
      if (error.message && error.message.includes('User is disabled')) {
        throw {
          status: 401,
          name: 'User Disabled',
          message: 'アカウントが無効になっています。管理者にお問い合わせください。',
        };
      } else {
        throw {
          status: 401,
          name: 'Authentication Failed',
          message: 'ログインIDもしくはパスワードが間違っています。',
        };
      }
    }
    
    if (error.name === 'UserNotFoundException') {
      throw {
        status: 401,
        name: 'User Not Found',
        message: 'ログインIDもしくはパスワードが間違っています。',
      };
    }
    
    // Re-throw custom errors
    if (error.status) {
      throw error;
    }
    
    // Generic error
    throw {
      status: 500,
      name: 'Login Error',
      message: 'ログインに失敗しました。しばらく時間をおいて再度お試しください。',
    };
  }
}

/**
 * Lambda handler function
 */
exports.handle = (e, ctx, cb) => {
  ctx.callbackWaitsForEmptyEventLoop = false;
  console.log('cognito-login:', e);
  
  loginMember(e)
    .then(result => Base.createSuccessResponse(cb, result))
    .catch(error => {
      console.error('Login failed:', error);
      const errorResponse = {
        status: error.status || 500,
        name: error.name || 'Login Error',
        message: error.message || 'ログインに失敗しました。',
      };
      return Base.createErrorResponse(cb, errorResponse);
    });
};
