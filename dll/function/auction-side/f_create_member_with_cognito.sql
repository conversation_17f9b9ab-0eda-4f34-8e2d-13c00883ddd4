CREATE OR REPLACE FUNCTION public.f_create_member_with_cognito (
    in_tenant_no bigint,
    in_free_field jsonb,
    in_email character varying
)
RETURNS TABLE(
    member_no bigint,
    user_no bigint,
    member_id character varying,
    user_id character varying
)
LANGUAGE plpgsql
COST 100
VOLATILE
ROWS 1
AS $BODY$

DECLARE
    v_member_no bigint;
    v_user_no bigint;
    v_member_id character varying;
    v_user_id character varying;
    v_member_seq bigint;
    v_user_seq bigint;

----------------------------------------------------------------------------------------------------
-- Cognito会員登録用の会員作成
----------------------------------------------------------------------------------------------------

BEGIN

    -- Get next member sequence
    SELECT COALESCE(MAX(member_no), 0) + 1 INTO v_member_seq
    FROM m_member 
    WHERE tenant_no = in_tenant_no;

    -- Get next user sequence  
    SELECT COALESCE(MAX(user_no), 0) + 1 INTO v_user_seq
    FROM m_user;

    -- Generate member_id and user_id
    v_member_id := 'M' || LPAD(v_member_seq::text, 8, '0');
    v_user_id := in_email;

    -- Insert member record
    INSERT INTO m_member (
        tenant_no,
        member_no,
        member_id,
        free_field,
        delete_flag,
        created_at,
        updated_at,
        created_by,
        updated_by
    ) VALUES (
        in_tenant_no,
        v_member_seq,
        v_member_id,
        in_free_field,
        '0',
        NOW(),
        NOW(),
        1, -- system user
        1  -- system user
    );

    -- Insert user record
    INSERT INTO m_user (
        user_no,
        member_no,
        user_id,
        password_hash,
        require_password_change,
        delete_flag,
        created_at,
        updated_at,
        created_by,
        updated_by
    ) VALUES (
        v_user_seq,
        v_member_seq,
        v_user_id,
        '', -- Password managed by Cognito
        '0',
        '0',
        NOW(),
        NOW(),
        1, -- system user
        1  -- system user
    );

    -- Set return values
    v_member_no := v_member_seq;
    v_user_no := v_user_seq;

    RETURN QUERY
    SELECT v_member_no, v_user_no, v_member_id, v_user_id;

END;

$BODY$;
