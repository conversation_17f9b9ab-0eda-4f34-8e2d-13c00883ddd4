CREATE OR REPLACE FUNCTION public.f_get_member_by_email (
    in_tenant_no bigint,
    in_email character varying
)
RETURNS TABLE(
    member_no bigint,
    user_no bigint,
    member_id character varying,
    user_id character varying,
    free_field jsonb,
    delete_flag character varying,
    created_at timestamp with time zone,
    updated_at timestamp with time zone
)
LANGUAGE plpgsql
COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 会員情報をメールアドレスで取得（Cognito用）
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT MM.member_no,
         MU.user_no,
         MM.member_id,
         MU.user_id,
         MM.free_field,
         MM.delete_flag,
         MM.created_at,
         MM.updated_at
    FROM m_member MM
    JOIN m_user MU ON MU.member_no = MM.member_no
   WHERE MM.tenant_no = in_tenant_no
     AND MM.free_field->>'email' = in_email
     AND MM.delete_flag = '0'
     AND MU.delete_flag = '0'
   LIMIT 1;

END;

$BODY$;
